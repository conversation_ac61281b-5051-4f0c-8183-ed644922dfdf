/**
 * crm-activity controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

export default factories.createCoreController(
  "api::crm-activity.crm-activity",
  ({ strapi }) => ({
    async phoneCallRegistration(ctx: Context) {
      try {
        const {
          document_type,
          subject,
          phone_call_category,
          disposition_code,
          start_date,
          end_date,
          initiator_code,
          owner_party_id,
          main_account_party_id,
          main_contact_party_id,
          activity_status,
          note,
          involved_parties,
        } = ctx.request.body;

        if (
          !document_type ||
          !subject ||
          !main_account_party_id ||
          !main_contact_party_id
        ) {
          return ctx.throw(
            400,
            "Missing required fields: document_type, subject, main_account_party_id, main_contact_party_id"
          );
        }

        const locale = "en";

        const newActivity = await strapi.db.transaction(async () => {
          let data: any = {
            document_type,
            subject,
            phone_call_category,
            disposition_code,
            start_date,
            end_date,
            initiator_code,
            owner_party_id,
            main_account_party_id,
            main_contact_party_id,
            activity_status,
            locale,
          };
          const activity = await strapi
            .query("api::crm-activity.crm-activity")
            .create({
              data,
            });

          data = {
            activity_id: activity.activity_id,
            note,
            is_global_note: true,
            locale,
          };

          await strapi.query("api::crm-note.crm-note").create({
            data,
          });

          if (Array.isArray(involved_parties)) {
            // ✅ Insert related involved_parties
            for (const involved_party of involved_parties) {
              data = {
                party_uuid: involved_party.party_uuid,
                party_type_code: involved_party.party_type_code,
                role_category_code: involved_party.role_category_code,
                role_code: involved_party.role_code,
                party_id: involved_party.party_id,
                activity_id: activity.activity_id,
                locale,
              };

              await strapi
                .query("api::crm-involved-party.crm-involved-party")
                .create({
                  data,
                });
            }
          }

          return activity;
        });

        return ctx.send({
          message: "Phone call and involved parties registered successfully",
          data: newActivity,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register Phone call and involved parties activity: ${error.message}`
        );
      }
    },
  })
);
